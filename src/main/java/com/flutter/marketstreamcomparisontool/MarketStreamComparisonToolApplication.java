package com.flutter.marketstreamcomparisontool;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.Import;

@SpringBootApplication
@ConfigurationPropertiesScan
public class MarketStreamComparisonToolApplication {
    public static void main(String[] args) {
        SpringApplication.run(MarketStreamComparisonToolApplication.class, args);
    }


}
