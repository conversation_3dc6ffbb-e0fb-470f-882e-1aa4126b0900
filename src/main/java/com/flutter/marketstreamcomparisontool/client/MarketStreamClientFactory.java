package com.flutter.marketstreamcomparisontool.client;

import com.ppb.platform.sb.fmg.MarketStreamClient;

public class MarketStreamClientFactory {

    public static MarketStreamClient createClient(String clientConfig, String consumerConfig, String consumerContainerConfig) throws Exception {
        return new MarketStreamClient.MarketStreamClientBuilder(clientConfig)
                .setProvidedConsumerConfig(consumerConfig)
                .setProvidedConsumerContainerConfig(consumerContainerConfig)
                .build();
    }
}
